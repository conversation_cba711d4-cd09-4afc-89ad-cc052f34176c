import { useState } from "react"
import { useForm } from "react-hook-form"
import { useAuth } from "@/hooks/useAuth"
import { Icon } from "@/components"
import { SocialLoginButton } from "@/components/auth"

export interface LoginFormProps {
  onLoginSuccess: () => void
}

const LoginForm = ({ onLoginSuccess }: LoginFormProps) => {
  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
  } = useForm<Api.Request.LoginInfo>()

  const socials = ["kakao", "google", "github"]
  const [showPassword, setShowPassword] = useState(false)
  const { userLogin } = useAuth()

  const onSubmit = (data: Api.Request.LoginInfo) => {
    userLogin(data, onLoginSuccess)
  }

  return (
    <form onSubmit={handleSubmit(onSubmit)} className="w-80">
      {/* 타이틀 */}
      <div className="flex flex-col items-center justify-center mb-6 mt-4">
        <Icon name="user" size={48} className="text-gray-300 mb-4" />
        <h2 className="text-2xl font-bold">로그인</h2>
      </div>

      <div className="space-y-4">
        {/* 이메일 입력 필드 */}
        <div>
          <div className="mt-1 relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <Icon name="email" className="h-5 w-5 text-gray-400" />
            </div>

            <input
              id="email"
              type="email"
              autoComplete="email"
              placeholder="이메일"
              className="appearance-none block w-full px-10 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-gray-500 focus:border-gray-500 sm:text-sm"
              {...register("email", {
                required: "이메일은 필수 입력 항목입니다.",
                pattern: {
                  value: /^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,}$/i,
                  message: "유효한 이메일 주소가 아닙니다.",
                },
              })}
            />
          </div>
          {errors.email && <p className="mt-1 text-xs text-red-500">{errors.email.message}</p>}
        </div>

        {/* 비밀번호 입력 필드 */}
        <div>
          <div className="mt-1 relative">
            <div className="pointer-events-none absolute inset-y-0 left-0 flex items-center pl-3">
              <Icon name="lock" className="h-5 w-5 text-gray-400" />
            </div>

            <input
              id="password"
              type={showPassword ? "text" : "password"}
              autoComplete="current-password"
              placeholder="비밀번호"
              className="appearance-none block w-full px-10 py-2 border border-gray-300 rounded-md shadow-sm placeholder-gray-400 focus:outline-none focus:ring-gray-500 focus:border-gray-500 sm:text-sm"
              {...register("password", {
                required: "비밀번호는 필수 입력 항목입니다.",
              })}
            />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute inset-y-0 right-0 pr-3 flex items-center text-gray-500"
            >
              <Icon size={20} name={showPassword ? "eye" : "eyeSlash"} />
            </button>
          </div>
          {errors.password && (
            <p className="mt-1 text-xs text-red-500">{errors.password.message}</p>
          )}
        </div>

        {/* 로그인 버튼 */}
        <div>
          <button
            type="submit"
            disabled={isSubmitting}
            className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-500 hover:bg-blue-600"
          >
            로그인
          </button>
        </div>

        {/* 구분선 */}
        <div className="relative flex items-center">
          <div className="flex-grow border-t border-gray-600"></div>
          <span className="mx-4 flex-shrink-0 text-sm text-gray-400">OR</span>
          <div className="flex-grow border-t border-gray-600"></div>
        </div>

        {/* 소셜 로그인 버튼 그룹 */}
        <div className="grid grid-cols-3 gap-3">
          {socials.map((social) => (
            <SocialLoginButton key={social} social={social} />
          ))}
        </div>
      </div>
    </form>
  )
}

export default LoginForm
