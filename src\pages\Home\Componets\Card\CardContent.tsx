import { Icon } from "@/components"
import { useAuth } from "@/hooks/useAuth"
import { useState } from "react"
import CardDetailModal from "./CardDetailModal"

const getBrightness = (hexColor: string): "dark" | "light" => {
  if (!hexColor || !hexColor.startsWith("#")) return "light"
  const cleanHex = hexColor.slice(1)
  if (cleanHex.length !== 6) return "light" // 유효성 검사 추가
  const rgb = parseInt(cleanHex, 16)
  const r = (rgb >> 16) & 0xff
  const g = (rgb >> 8) & 0xff
  const b = (rgb >> 0) & 0xff
  const luma = 0.2126 * r + 0.7152 * g + 0.0722 * b
  return luma < 140 ? "dark" : "light" // 임계값 조정으로 대비를 더 명확하게
}

const getDerivedCardStyles = (bgColor: string) => {
  const brightness = getBrightness(bgColor)
  if (brightness === "dark") {
    return {
      textColorClass: "text-white",
      descriptionColorClass: "text-gray-300",
      buttonClasses: "bg-gray-700 text-white hover:bg-gray-600",
    }
  }
  return {
    textColorClass: "text-gray-900",
    descriptionColorClass: "text-gray-600",
    buttonClasses: "border border-gray-300 text-gray-700 hover:bg-gray-50",
  }
}

const CardContent = ({
  id,
  icons,
  title,
  description,
  bgColor,
  onEditClick,
  onDelete,
}: Card.CardContentProps) => {
  const styles = getDerivedCardStyles(bgColor)
  const handleEdit = () => onEditClick({ id, icons: icons, title, description, bgColor })
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false)
  const { isAuthenticated } = useAuth()

  const handleDelete = () => {
    if (window.confirm(`'${title}' 항목을 정말로 삭제하시겠습니까?`)) {
      onDelete(id)
    }
  }

  return (
    <div
      style={{ backgroundColor: bgColor }}
      className={`p-6 rounded-2xl shadow-lg flex flex-col transition-colors duration-300`}
    >
      {/* 삭제 */}
      {isAuthenticated && (
        <button
          onClick={handleDelete}
          className={`absolute top-4 right-4 w-8 h-8 rounded-full flex items-center justify-center
                     bg-black bg-opacity-20 hover:bg-opacity-30 transition-colors duration-200
                     ${styles.textColorClass}`}
          aria-label="Delete item"
        >
          <Icon name="close" size={20} />
        </button>
      )}

      {/* 아이콘 */}
      <div className="flex justify-between items-start mb-4">
        <div className="flex flex-wrap gap-1">
          {icons.map((iconName) => (
            <div key={iconName} className="relative group flex justify-center">
              <div
                className={`w-12 h-12 rounded-full flex items-center justify-center ${
                  styles.textColorClass
                } bg-black bg-opacity-10 border-2 ${
                  getBrightness(bgColor) === "dark" ? "border-gray-800" : "border-white"
                }`}
              >
                <Icon name={iconName} size={28} className={styles.textColorClass} />
              </div>
              {/* --- 커스텀 툴팁 --- */}
              <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 w-auto min-w-max px-2 py-1 text-xs font-semibold text-white bg-gray-900 rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity duration-300 whitespace-nowrap z-10">
                {iconName}
                {/* 툴팁 꼬리 */}
                <svg
                  className="absolute text-gray-900 h-2 w-full left-0 top-full"
                  x="0px"
                  y="0px"
                  viewBox="0 0 255 255"
                  xmlSpace="preserve"
                >
                  <polygon className="fill-current" points="0,0 127.5,127.5 255,0" />
                </svg>
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* 제목과 설명 */}
      <div className="flex-grow">
        <h3 className={`text-xl font-bold ${styles.textColorClass}`}>{title}</h3>
        <p
          className={`mt-2 text-sm ${styles.descriptionColorClass} line-clamp-3 whitespace-pre-line`}
        >
          {description}
        </p>
      </div>

      {/* 하단 버튼 */}
      {isAuthenticated ? (
        <div className="mt-6 grid grid-cols-2 gap-2">
          <button
            onClick={() => setIsModalOpen(true)}
            className={`w-full py-2 px-4 rounded-lg font-semibold transition-all duration-200 ${styles.buttonClasses}`}
          >
            상세보기
          </button>
          <button
            onClick={handleEdit}
            className={`w-full py-2 px-4 rounded-lg font-semibold transition-all duration-200 ${styles.buttonClasses}`}
          >
            수정
          </button>
        </div>
      ) : (
        <div className="mt-6">
          <button
            onClick={() => setIsModalOpen(true)}
            className={`w-full py-2 px-4 rounded-lg font-semibold transition-all duration-200 ${styles.buttonClasses}`}
          >
            상세보기
          </button>
        </div>
      )}

      {/* 상세보기 모달 */}
      {isModalOpen && (
        <CardDetailModal
          data={{
            mainIcon: icons[0],
            title,
            description,
            technologiesUsed: [],
          }}
          onClose={() => setIsModalOpen(false)}
        />
      )}
    </div>
  )
}

export default CardContent
