import { Icon } from "@/components"

interface CardDetailData {
  mainIcon: string
  title: string
  description: string
  technologiesUsed: { icon: string; name: string }[]
}

interface CardDetailModalProps {
  data: CardDetailData
  onClose: () => void
}

const CardDetailModal = ({ data, onClose }: CardDetailModalProps) => {
  return (
    // 모달 오버레이
    <div
      className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50"
      onClick={onClose} // 배경 클릭 시 닫기
    >
      {/* 모달 컨텐츠 */}
      <div
        className="relative w-full max-w-2xl bg-[#2C2C3A] text-white rounded-2xl shadow-xl p-8 m-4"
        onClick={(e) => e.stopPropagation()} // 컨텐츠 클릭 시 닫히는 것 방지
      >
        {/* 닫기 버튼 */}
        <button
          onClick={onClose}
          className="absolute top-4 right-4 w-8 h-8 rounded-full flex items-center justify-center
                     bg-black bg-opacity-20 hover:bg-opacity-40 transition-colors"
          aria-label="Close modal"
        >
          <Icon name="close" size={20} />
        </button>

        {/* 아이콘과 제목 */}
        <div className="flex items-center gap-6 mb-8">
          <div className="w-20 h-20 bg-gray-700 rounded-full flex items-center justify-center border-2 border-gray-800 shrink-0">
            <Icon name={data.mainIcon} size={48} />
          </div>

          {/* 제목 */}
          <h2 className="text-3xl font-bold">{data.title}</h2>
        </div>

        {/* 상세 설명 */}
        <p className="text-gray-300 whitespace-pre-line">{data.description}</p>
      </div>
    </div>
  )
}

export default CardDetailModal
