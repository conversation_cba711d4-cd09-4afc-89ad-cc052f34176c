import { login, logout, reissueAccessToken } from "@/service/api/auth"
import { useAuthStore } from "@/store/auth"
import { useCallback } from "react"

export const useAuth = () => {
  const { storeLogin, storeLogout, isAuthenticated, userInfo } = useAuthStore()

  const userLogin = useCallback(
    async (data: Api.Request.LoginInfo, onLoginSuccess: () => void) => {
      const response = await login({ email: data.email, password: data.password })
      storeLogin(response)
      onLoginSuccess()
    },
    [storeLogin],
  )

  const userLogout = useCallback(async () => {
    const response = await logout()

    if (response.code == "SU") {
      storeLogout()
    }
  }, [storeLogout])

  const checkAuthStatus = useCallback(async () => {
    const userInfo = await reissueAccessToken()
    storeLogin(userInfo)

    if (!userInfo) {
      storeLogout()
    }
  }, [storeLogin, storeLogout])

  return {
    userLogin,
    userLogout,
    checkAuthStatus,
    isAuthenticated,
    userInfo,
  }
}
