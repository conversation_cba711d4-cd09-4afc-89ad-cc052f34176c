import { Icon } from "@/components"
import { categorizedTechIcons, type IconName } from "@/components/icon"
import React, { useEffect, useState } from "react"

const categories = Object.keys(categorizedTechIcons)

const CardFormModal = ({ isOpen, onClose, onSubmit, initialData }: Card.CardFormModalProps) => {
  const [title, setTitle] = useState<string>("")
  const [description, setDescription] = useState<string>("")
  const [bgColor, setBgColor] = useState<string>("#1F2937")
  const [selectedIcons, setSelectedIcons] = useState<IconName[]>([])
  const [error, setError] = useState("")

  const [activeTab, setActiveTab] = useState<string>(categories[0] || "")

  useEffect(() => {
    if (isOpen) {
      if (initialData) {
        setTitle(initialData.title)
        setDescription(initialData.description)
        setBgColor(initialData.bgColor)
        setSelectedIcons(initialData.icons)
      } else {
        // Reset form for new card
        setTitle("")
        setDescription("")
        setBgColor("#1F2937")
        setSelectedIcons([])
      }
      setError("") // Clear previous errors
    }
  }, [isOpen, initialData])

  if (!isOpen) return null

  // 아이콘 선택/해제 토글 핸들러
  const handleIconClick = (iconName: IconName) => {
    setSelectedIcons((prev) =>
      prev.includes(iconName) ? prev.filter((icon) => icon !== iconName) : [...prev, iconName],
    )
  }

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    if (!title.trim() || !description.trim()) {
      setError("제목과 설명은 필수 항목입니다.")
      return
    }

    onSubmit({ title, description, bgColor, icons: selectedIcons })
    onClose()
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-60 flex justify-center items-center z-50 p-4">
      <div className="bg-white p-8 rounded-2xl shadow-xl w-full max-w-2xl animate-fade-in-up">
        <h2 className="text-2xl font-bold mb-6 text-gray-800">
          {initialData ? "프로젝트 수정" : "새 프로젝트 추가"}
        </h2>
        <form onSubmit={handleSubmit} noValidate>
          {/* 제목 */}
          <div className="mb-4">
            <label htmlFor="title" className="block text-sm font-medium text-gray-700">
              제목
            </label>
            <input
              type="text"
              id="title"
              value={title}
              onChange={(e) => setTitle(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-400 focus:border-gray-400"
            />
          </div>

          {/* 설명 */}
          <div className="mb-4">
            <label htmlFor="description" className="block text-sm font-medium text-gray-700">
              설명
            </label>
            <textarea
              id="description"
              rows={4}
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-gray-400 focus:border-gray-400"
            />
          </div>

          {/* 카테고리 */}
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 mb-2">사용 기술</label>
            <div className="border border-gray-200 rounded-md">
              {/* 2. 탭 버튼 UI */}
              <div className="flex border-b border-gray-200 bg-gray-50 rounded-t-md">
                {categories.map((category) => (
                  <button
                    key={category}
                    type="button"
                    onClick={() => setActiveTab(category)}
                    className={`flex-1 sm:flex-none px-4 py-2.5 text-sm font-medium transition-all duration-200 ease-in-out focus:outline-none ${
                      activeTab === category
                        ? "border-b-2 border-blue-500 text-blue-600 bg-white"
                        : "text-gray-500 hover:bg-gray-100 hover:text-gray-800"
                    }`}
                  >
                    {category}
                  </button>
                ))}
              </div>

              {/* 3. 활성화된 탭의 컨텐츠 (아이콘 목록) */}
              <div className="p-4">
                {activeTab && categorizedTechIcons[activeTab] && (
                  <div className="grid grid-cols-5 sm:grid-cols-8 md:grid-cols-10 gap-2 animate-fade-in">
                    {Object.keys(categorizedTechIcons[activeTab]).map((name) => (
                      <div key={name} className="relative group flex justify-center">
                        <button
                          type="button"
                          onClick={() => handleIconClick(name as IconName)}
                          className={`flex w-full justify-center items-center p-2 rounded-lg transition-colors ${
                            selectedIcons.includes(name as IconName)
                              ? "bg-blue-100 ring-2 ring-blue-400"
                              : "bg-gray-50 hover:bg-gray-200"
                          }`}
                        >
                          <Icon name={name as IconName} size={28} className="text-gray-700" />
                        </button>
                        {/* 툴팁 */}
                        <div className="absolute bottom-full left-1/2 -translate-x-1/2 mb-2 w-auto min-w-max px-2 py-1 text-xs font-semibold text-white bg-gray-900 rounded-md opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-opacity duration-300 whitespace-nowrap z-10">
                          {name}
                          <svg
                            className="absolute text-gray-900 h-2 w-full left-0 top-full"
                            viewBox="0 0 255 255"
                          >
                            <polygon className="fill-current" points="0,0 127.5,127.5 255,0" />
                          </svg>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* 배경 색상 */}
          <div className="mb-6">
            <label htmlFor="bgColor" className="block text-sm font-medium text-gray-700">
              배경 색상
            </label>
            <input
              type="color"
              id="bgColor"
              value={bgColor}
              onChange={(e) => setBgColor(e.target.value)}
              className="mt-1 w-full h-10 p-1 border border-gray-300 rounded-md cursor-pointer"
            />
          </div>

          {error && <p className="text-red-500 text-sm mb-4 text-center">{error}</p>}

          <div className="flex justify-end gap-4">
            <button
              type="button"
              onClick={onClose}
              className="px-5 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-200"
            >
              취소
            </button>
            <button
              type="submit"
              className="px-5 py-2 text-sm font-medium text-white bg-blue-500 border border-transparent rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-200"
            >
              {initialData ? "저장하기" : "추가하기"}
            </button>
          </div>
        </form>
      </div>
    </div>
  )
}

export default CardFormModal
