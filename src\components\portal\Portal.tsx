import { useEffect, useState } from "react"
import { createPortal } from "react-dom"

export interface PortalProps {
  children: React.ReactNode
}

const Portal = ({ children }: PortalProps) => {
  const [element, setElement] = useState<HTMLElement | null>(null)

  useEffect(() => {
    setElement(document.getElementById("modal-root"))
  }, [])

  if (!element) {
    return null
  }

  return createPortal(children, element)
}

export default Portal
