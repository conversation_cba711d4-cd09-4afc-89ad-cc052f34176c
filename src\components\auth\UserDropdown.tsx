import { useEffect, useRef, useState } from "react"
import { useAuth } from "@/hooks/useAuth"
import { Icon } from "@/components"

const UserDropdown = () => {
  const { userInfo, userLogout } = useAuth()
  const [isOpen, setIsOpen] = useState(false)
  const dropdownRef = useRef<HTMLDivElement>(null)

  const toggleDropdown = () => {
    setIsOpen(!isOpen)
  }

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false)
      }
    }
    document.addEventListener("mousedown", handleClickOutside)
    return () => {
      document.removeEventListener("mousedown", handleClickOutside)
    }
  }, [dropdownRef])

  const handleLogout = () => {
    userLogout()
    setIsOpen(false)
  }

  return (
    // relative: 드롭다운 메뉴 위치의 기준점
    <div className="relative inline-block text-left" ref={dropdownRef}>
      {/* 사용자 이름 버튼 */}
      <button
        onClick={toggleDropdown}
        className="inline-flex justify-center items-center p-2 text-base font-medium text-gray-700 bg-transparent rounded-md hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-200 transition-colors"
        aria-haspopup="true"
        aria-expanded={isOpen}
      >
        <span>{userInfo!.username}</span>
        {/* 아래 화살표 아이콘 (선택 사항) */}
        <svg
          className="-mr-1 ml-2 h-5 w-5"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
          aria-hidden="true"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>

      {isOpen && (
        <div
          className="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg bg-white ring-1 ring-black ring-opacity-5 focus:outline-none z-50 transition ease-out duration-100"
          role="menu"
          aria-orientation="vertical"
          aria-labelledby="menu-button"
        >
          <div className="py-1" role="none">
            {/* 메뉴 아이템들 */}

            <button
              onClick={handleLogout}
              className="text-gray-700 flex items-center w-full text-left px-4 py-2 text-sm hover:bg-gray-100"
              role="menuitem"
            >
              <Icon name="logout" className="mr-2 h-5 w-5" />
              로그아웃
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default UserDropdown
