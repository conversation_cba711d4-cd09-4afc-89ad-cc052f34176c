import { authRequest } from "@/service/request"

export const login = async (authObj: Api.Request.LoginInfo) => {
  return await authRequest.post<Api.Response.UserInfo>("/login", authObj, { withCredentials: true })
}

export const logout = async () => {
  return await authRequest.post<Api.Common.BaseResponse>("/logout", null, { withCredentials: true })
}

export const reissueAccessToken = async () => {
  return await authRequest.post<Api.Response.UserInfo>("/reissue-access-token", null, {
    withCredentials: true,
  })
}
