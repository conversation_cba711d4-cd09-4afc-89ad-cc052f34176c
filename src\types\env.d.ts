declare global {
  /**
   * 서비스 환경 종류
   * dev: 개발 환경
   * prod: 운영 환경
   */
  type ServiceEnvType = "dev" | "prod"
  // 서비스 환경
  interface ServiceEnvConfig {
    // 서버 주소
    serverURL: string
  }

  interface ImportMetaEnv {
    readonly VITE_BASE_URL: string
    readonly VITE_APP_TITLE: string
    readonly VITE_AUTH_ROUTE_MODE: "static" | "dynamic"
    readonly VITE_APP_VERSION: string
    readonly VITE_SERVICE_ENV?: ServiceEnvType
    readonly VITE_ROUTE_HOME_PATH: string
    readonly VITE_OAUTH_REDIRECT_PATH: string
  }

  interface ImportMeta {
    readonly env: ImportMetaEnv
  }
}

export { ServiceEnvType, ServiceEnvConfig }
