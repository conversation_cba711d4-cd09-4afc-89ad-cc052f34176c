import type { FunctionComponent, SVGProps } from "react"
import { FaEye, FaEyeSlash, FaUserCircle } from "react-icons/fa"
import { FcGoogle } from "react-icons/fc"
import { MdOutlineEmail, MdOutlineLogout } from "react-icons/md"
import { RiKakaoTalkFill, RiGithubFill, RiCloseFill, RiLock2Line } from "react-icons/ri"

export type IconComponentType = FunctionComponent<SVGProps<SVGSVGElement>>

export const staticIconMap = {
  eye: FaEye,
  eyeSlash: FaEyeSlash,
  user: FaUserCircle,
  google: FcGoogle,
  lock: RiLock2Line,
  email: MdOutlineEmail,
  logout: MdOutlineLogout,
  close: RiCloseFill,
  kakao: RiKakaoTalkFill,
  github: RiGithubFill,
}

export type StaticIconName = keyof typeof staticIconMap

const techIconCategories = {
  language: "language",
  framework: "framework",
  etc: "etc",
}

type TechCategory = keyof typeof techIconCategories

const loadTechIcons = (category: TechCategory): Record<string, IconComponentType> => {
  // Vite의 `import.meta.glob`을 사용하여 동적으로 파일을 가져옵니다.
  const modules = import.meta.glob("/src/assets/icons/tech/**/*.svg", {
    query: "?react",
    eager: true,
  })

  const icons: Record<string, IconComponentType> = {}

  for (const path in modules) {
    if (path.includes(`/tech/${category}/`)) {
      const fileName = path.split("/").pop()?.replace(".svg", "")
      if (fileName) {
        icons[fileName.toLowerCase()] = (modules[path] as { default: IconComponentType }).default
      }
    }
  }
  return icons
}

export const categorizedTechIcons = Object.entries(techIconCategories).reduce(
  (acc, [key, displayName]) => {
    const icons = loadTechIcons(key as TechCategory)
    // 아이콘이 하나 이상 있는 카테고리만 추가합니다.
    if (Object.keys(icons).length > 0) {
      acc[displayName] = icons
    }
    return acc
  },
  {} as Record<string, Record<string, IconComponentType>>,
)

const allTechIcons = Object.values(categorizedTechIcons).reduce(
  (acc, icons) => ({ ...acc, ...icons }),
  {},
)

export const iconMap: Record<string, IconComponentType> = {
  ...staticIconMap,
  ...allTechIcons,
}

export type IconName = keyof typeof iconMap

export interface IconProps extends SVGProps<SVGSVGElement> {
  name: IconName
  size?: number
  className?: string
}
