import { iconMap, type IconProps } from "./index"

const Icon = ({ name, size = 24, className, ...props }: IconProps) => {
  const IconComponent = iconMap[name]

  if (!IconComponent) {
    console.warn(`Icon not found: ${name}`)
    return null
  }

  const style = size ? { width: size, height: size } : {}

  return <IconComponent style={style} className={className} {...props} />
}

export default Icon
