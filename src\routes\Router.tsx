import { Route, Routes } from "react-router-dom"
import { MAIN_PATH, OAUTH_REDIRECT_PATH } from "@/constants"
import { lazy } from "react"

// 메인 페이지
const HomeView = lazy(() => import("@/pages/Home"))
const OAuthRedirectHandler = lazy(() => import("@/components/auth/OAuthRedirectHandler"))

const Router = () => {
  return (
    <Routes>
      <Route path={MAIN_PATH} element={<HomeView />} />
      <Route path={OAUTH_REDIRECT_PATH} element={<OAuthRedirectHandler />} />
    </Routes>
  )
}

export default Router
