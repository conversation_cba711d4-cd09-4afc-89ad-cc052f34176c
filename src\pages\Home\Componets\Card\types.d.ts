declare namespace Card {
  interface CardData {
    id: string
    icons: IconName[] // 실제로는 IconName 타입
    title: string
    description: string
    bgColor: string
  }

  interface CardContentProps extends CardData {
    onEditClick: (card: CardData) => void
    onDelete: (id: string) => void
  }

  interface CardFormModalProps {
    isOpen: boolean
    onClose: () => void
    onSubmit: (data: Omit<CardData, "id">) => void
    initialData?: CardData | null
  }
}
