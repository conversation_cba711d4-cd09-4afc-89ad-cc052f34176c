import Icon from "@/components/icon/Icon"

interface SocialLoginButtonProps {
  social: string
}

const SocialLoginButton = ({ social }: SocialLoginButtonProps) => {
  const redirectPath = import.meta.env.VITE_OAUTH_REDIRECT_PATH

  return (
    <a
      href={`${redirectPath}/${social}`}
      className="inline-flex w-full items-center justify-center rounded-md border border-gray-400 bg-transparent p-2 hover:bg-gray-200"
    >
      <Icon name={social} color="yellow" className="h-5 w-5" />
    </a>
  )
}

export default SocialLoginButton
