import axios, { type AxiosRequestConfig, type AxiosResponse } from "axios"
import type CustomAxiosInstance from "./instance"
import { useAuthStore } from "@/store/auth"

// 커스텀 요청 설정 타입
interface CreateRequestConfig extends AxiosRequestConfig {
  baseURL: string
}

// Axios 인스턴스를 생성하는 함수
export const createRequest = (config: CreateRequestConfig): CustomAxiosInstance => {
  const instance = axios.create({
    baseURL: config.baseURL,
    timeout: 10000,
    headers: {
      "Content-Type": "application/json",
      ...config.headers,
    },
  }) as CustomAxiosInstance

  // 요청 인터셉터
  instance.interceptors.request.use(
    (requestConfig) => {
      const { userInfo } = useAuthStore.getState()
      const accessToken = userInfo?.accessToken

      if (accessToken) {
        requestConfig.headers.Authorization = `Bearer ${accessToken}`
      }

      return requestConfig
    },
    (error) => {
      console.error("요청 중 오류 발생:", error)
      return Promise.reject(error)
    },
  )

  // 응답 인터셉터
  instance.interceptors.response.use(
    (response: AxiosResponse) => response.data, // 데이터만 반환
    (error) => {
      if (error.response) {
        console.error("응답 오류 발생:", error.response.data)
      } else {
        console.error("네트워크 오류 발생:", error.message)
      }
      return Promise.reject(error)
    },
  )

  return instance
}
