import { useEffect } from "react"
import { Icon, Portal, LoginForm } from "@/components"

export interface LoginModalProps {
  isOpen: boolean
  onClose: () => void
}
const LoginModal = ({ isOpen, onClose }: LoginModalProps) => {
  useEffect(() => {
    if (isOpen) {
      document.body.classList.add("modal-open")
    } else {
      document.body.classList.remove("modal-open")
    }

    return () => {
      document.body.classList.remove("modal-open")
    }
  }, [isOpen])

  if (!isOpen) return null

  return (
    <Portal>
      <div onClick={onClose} className="fixed inset-0 bg-black/50 flex justify-center items-center">
        <div
          onClick={(e) => e.stopPropagation()}
          className="bg-white rounded-lg shadow-xl p-5 relative"
        >
          <button onClick={onClose} className="absolute top-2 right-2">
            <Icon name="close" size={24} />
          </button>
          <LoginForm onLoginSuccess={onClose} />
        </div>
      </div>
    </Portal>
  )
}

export default LoginModal
