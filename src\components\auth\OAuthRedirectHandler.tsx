import { useEffect } from "react"
import { useSearchParams, useNavigate } from "react-router-dom"

const OAuthRedirectHandler = () => {
  const [searchParams] = useSearchParams()
  const navigate = useNavigate()

  useEffect(() => {
    const accessToken = searchParams.get("accessToken")
    const refreshToken = searchParams.get("refreshToken") // 백엔드 핸들러 수정 후 추가됨

    if (accessToken) {
      // 1. 액세스 토큰을 받아옵니다.
      // 2. 이 토큰을 사용하여 사용자 정보를 가져오는 API를 호출합니다.
      //    (useAuth의 checkAuthStatus가 유사한 역할을 하므로 이를 활용하거나,
      //     storeLogin이 토큰만으로 상태를 업데이트하도록 수정이 필요할 수 있습니다.)

      // 현재 useAuth 구조를 최대한 활용하는 방법:
      // 먼저 토큰을 저장하고, 사용자 정보는 checkAuthStatus를 통해 가져오게 합니다.
      //   const tempUserInfo = {
      // accessToken: accessToken,
      // refreshToken과 사용자 정보(id, role 등)는
      // 이 토큰을 사용해 /api/user/me 같은 엔드포인트에서 가져와야 합니다.
      // 여기서는 임시로 토큰만 저장하고 바로 이동합니다.
      //   }

      // Zustand 스토어에 직접 로그인 정보 저장
      //   storeLogin(tempUserInfo)

      // 로그인 성공 후 메인 페이지로 이동
      navigate("/")
    }
  }, [searchParams, navigate])

  return (
    <div>
      <p>로그인 처리 중입니다. 잠시만 기다려주세요...</p>
      {/* 스피너와 같은 로딩 UI를 보여주면 좋습니다. */}
    </div>
  )
}

export default OAuthRedirectHandler
