import { create } from "zustand"
import { initAuthStoreState } from "./helpers"

interface AuthStoreState extends Store.Auth.StoreProps {
  storeLogin: (userInfo: Api.Response.UserInfo) => void
  storeLogout: () => void
}

export const useAuthStore = create<AuthStoreState>()((set) => ({
  ...initAuthStoreState(),
  storeLogin: (userInfo) => {
    set({ userInfo: userInfo, isAuthenticated: true })
  },
  storeLogout: () => {
    set({ userInfo: undefined, isAuthenticated: false })
  },
}))
