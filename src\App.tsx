import { lazy, Suspense, useEffect, useRef } from "react"
import { useAuth } from "@/hooks/useAuth"
import Header from "@/components/layout/Header"

const Router = lazy(() => import("@/routes/Router"))
const Footer = lazy(() => import("@/components/layout/Footer"))

const App = () => {
  const { checkAuthStatus } = useAuth()
  const isAuthChecked = useRef(false)

  useEffect(() => {
    if (isAuthChecked.current) return

    isAuthChecked.current = true
    checkAuthStatus()
  }, [checkAuthStatus])

  return (
    <div id="wrap">
      <Suspense fallback={null}>
        <Header />
        <main className="main-content max-w-7xl mx-auto px-4 py-8">
          <Router />
        </main>
        <Footer />
      </Suspense>
    </div>
  )
}

export default App
