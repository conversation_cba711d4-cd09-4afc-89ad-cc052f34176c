import { useCallback, useState } from "react"
import { CardContent, CardFormModal, AddCardButton } from "@/pages/Home/Componets/Card"

const Card = () => {
  const [cardData, setCardData] = useState<Card.CardData[]>([
    {
      id: "1",
      icons: [
        "csharp",
        "net",
        "python",
        "fastapi",
        "typescript",
        "react",
        "neo4j",
        "mongodb",
        "elastic search",
      ],
      title: "APT 성능개량 프로젝트",
      description:
        "사용 기술: Python (FastAPI), React (TypeScript)\n주요 업무:\n  - MITRE ATT&CK 매트릭스 테이블 구현\n  - Neo4J, MongoDB, Elastic Search 데이터를 기반으로 데이터 시각화 작업 진행 (차트 라이브러리를 활용한 데이터 시각화 그래프 구현)\n  - RestAPI 설계 및 구현",
      bgColor: "#1F2937",
    },
    {
      id: "2",
      icons: ["csharp", "net"],
      title: "Link-22 체계개발사업",
      description:
        "사용 기술: C# (WinForms - DevExpress)\n주요 업무:\n  - 화면 UI 구현 및 마샬링을 통한 데이터 송수신 처리\n  - 단위 테스트 및 통합 테스트 진행",
      bgColor: "#FFFFFF",
    },
  ])
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false)
  const [editingCard, setEditingCard] = useState<Card.CardData | null>(null)

  const handleOpenCreateModal = () => {
    setEditingCard(null)
    setIsModalOpen(true)
  }

  // useCallback으로 함수 재생성 방지
  const handleOpenEditModal = useCallback((cardToEdit: Card.CardData) => {
    setEditingCard(cardToEdit)
    setIsModalOpen(true)
  }, [])

  const handleDeleteCard = (id: string) => {
    setCardData((currentCards) => currentCards.filter((card) => card.id !== id))
  }

  const handleCloseModal = () => {
    setIsModalOpen(false)
  }

  const handleFormSubmit = (formData: Omit<Card.CardData, "id">) => {
    if (editingCard) {
      // 수정 모드
      setCardData(
        cardData.map((card) => (card.id === editingCard.id ? { ...card, ...formData } : card)),
      )
    } else {
      // 생성 모드
      setCardData((prev) => [...prev, { ...formData, id: String(Date.now()) }])
    }
    handleCloseModal()
  }

  return (
    <>
      <div
        id="card"
        className="grid grid-cols-1 xl:grid-cols-2 gap-6 max-h-[800px] overflow-y-auto scrollbar-hide"
      >
        {cardData &&
          cardData.map((item) => (
            <CardContent
              key={item.id}
              {...item}
              onEditClick={handleOpenEditModal}
              onDelete={handleDeleteCard}
            />
          ))}
        <AddCardButton onClick={handleOpenCreateModal} />
      </div>

      <CardFormModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        onSubmit={handleFormSubmit}
        initialData={editingCard}
      />
    </>
  )
}

export default Card
