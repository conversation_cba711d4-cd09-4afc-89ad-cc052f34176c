import { Tiptap } from "@/pages/Home/Componets/Hero"
import { useAuth } from "@/hooks/useAuth"
import { useState } from "react"

const Hero = () => {
  const { isAuthenticated } = useAuth()

  // Hero 컴포넌트가 에디터의 콘텐츠 상태를 관리합니다.
  const [heroContent, setHeroContent] = useState(`
    <h1>웹 개발자 포트폴리오</h1>
    <p>안녕하세요! <strong>C#</strong>, <strong>Java</strong>, 그리고 <strong>JavaScript</strong>에 익숙한 웹 개발자입니다. 클라우드 네이티브 기술을 활용하여 확장 가능하고 효율적인 서비스를 구축하는 데 관심이 많습니다.</p>
    <h2>주요 기술 스택</h2>
    <ul>
      <li><strong>언어:</strong> C#, Java, JavaScript, TypeScript</li>
    </ul>
    <h2>진행한 프로젝트 목록</h2>
    <ol>
      <li><strong>사내 관리자 페이지 개발:</strong> ASP.NET MVC와 MSSQL을 사용한 ERP 시스템.</li>
    </ol>
    <p>더 자세한 정보는 제 <a href="https://github.com/waz6432">GitHub</a>를 방문해주세요.</p>
  `)

  // 에디터 내용이 변경될 때마다 호출되어 상태를 업데이트하는 함수
  const handleContentUpdate = (newContent: string) => {
    setHeroContent(newContent)
    // 여기에 나중에 API를 통해 서버에 저장하는 로직을 추가할 수 있습니다.
    console.log("Updated Content:", newContent)
  }

  return (
    <section id="hero" className="flex items-start">
      <img src="https://placehold.co/50" alt="Profile" className="rounded-full w-32 h-32" />
      <Tiptap content={heroContent} editable={isAuthenticated} onUpdate={handleContentUpdate} />
    </section>
  )
}

export default Hero
